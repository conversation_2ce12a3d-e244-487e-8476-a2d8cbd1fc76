"""
MCV检测算法实现
基于红细胞平均面积计算平均体积(MCV)的校准和检测系统
"""

import numpy as np
import pickle
import json
from typing import Tuple, List, Optional, Dict, Union, Literal
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline
import matplotlib.pyplot as plt


class MCVDetector:
    """
    MCV检测器类

    使用二次多项式模型: MCV = A * (面积)^2 + B * 面积 + C
    来建立红细胞面积与MCV之间的关系
    """

    def __init__(self):
        """初始化MCV检测器"""
        self.coefficients = None  # 存储校准系数 [A, B, C]
        self.calibration_stats = None  # 存储校准统计信息
        self.is_calibrated = False

    def calibrate(self, areas: np.ndarray, reference_mcv: np.ndarray) -> Dict:
        """
        校准算法 - 拟合二次多项式模型

        Args:
            areas: 红细胞平均面积数组 (单位: μm²)
            reference_mcv: 参考MCV值数组 (单位: fL)

        Returns:
            校准统计信息字典
        """
        if len(areas) != len(reference_mcv):
            raise ValueError("面积数据和MCV数据长度必须相同")

        if len(areas) < 3:
            raise ValueError("校准至少需要3个数据点")

        # 转换为numpy数组
        areas = np.array(areas)
        reference_mcv = np.array(reference_mcv)

        # 构建设计矩阵 [面积^2, 面积, 1]
        X = np.column_stack([areas**2, areas, np.ones(len(areas))])

        # 使用最小二乘法拟合
        self.coefficients = np.linalg.lstsq(X, reference_mcv, rcond=None)[0]

        # 设置校准状态为True，这样predict_mcv方法就可以正常工作
        self.is_calibrated = True

        # 计算预测值
        predicted_mcv = self.predict_mcv(areas)

        # 计算统计指标
        r2 = r2_score(reference_mcv, predicted_mcv)
        rmse = np.sqrt(mean_squared_error(reference_mcv, predicted_mcv))
        mae = np.mean(np.abs(reference_mcv - predicted_mcv))

        self.calibration_stats = {
            'r2_score': r2,
            'rmse': rmse,
            'mae': mae,
            'n_samples': len(areas),
            'coefficients': {
                'A': self.coefficients[0],
                'B': self.coefficients[1],
                'C': self.coefficients[2]
            }
        }

        print(f"校准完成!")
        print(f"模型方程: MCV = {self.coefficients[0]:.6f} * 面积² + {self.coefficients[1]:.6f} * 面积 + {self.coefficients[2]:.6f}")
        print(f"R² = {r2:.4f}, RMSE = {rmse:.4f} fL, MAE = {mae:.4f} fL")

        return self.calibration_stats

    def predict_mcv(self, areas: np.ndarray) -> np.ndarray:
        """
        预测MCV值

        Args:
            areas: 红细胞平均面积数组 (单位: μm²)

        Returns:
            预测的MCV值数组 (单位: fL)
        """
        if not self.is_calibrated:
            raise RuntimeError("模型尚未校准，请先调用calibrate()方法")

        areas = np.array(areas)

        # 应用二次多项式模型: MCV = A * 面积² + B * 面积 + C
        mcv_predicted = (self.coefficients[0] * areas**2 +
                        self.coefficients[1] * areas +
                        self.coefficients[2])

        return mcv_predicted

    def predict_single(self, area: float) -> float:
        """
        预测单个样本的MCV值

        Args:
            area: 红细胞平均面积 (单位: μm²)

        Returns:
            预测的MCV值 (单位: fL)
        """
        return float(self.predict_mcv([area])[0])

    def save_model(self, filepath: str):
        """
        保存校准模型

        Args:
            filepath: 保存路径
        """
        if not self.is_calibrated:
            raise RuntimeError("模型尚未校准，无法保存")

        model_data = {
            'coefficients': self.coefficients.tolist(),
            'calibration_stats': self.calibration_stats,
            'is_calibrated': self.is_calibrated
        }

        if filepath.endswith('.json'):
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(model_data, f, indent=2, ensure_ascii=False)
        else:
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)

        print(f"模型已保存到: {filepath}")

    def load_model(self, filepath: str):
        """
        加载校准模型

        Args:
            filepath: 模型文件路径
        """
        if filepath.endswith('.json'):
            with open(filepath, 'r', encoding='utf-8') as f:
                model_data = json.load(f)
        else:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)

        self.coefficients = np.array(model_data['coefficients'])
        self.calibration_stats = model_data['calibration_stats']
        self.is_calibrated = model_data['is_calibrated']

        print(f"模型已从 {filepath} 加载")
        print(f"模型方程: MCV = {self.coefficients[0]:.6f} * 面积² + {self.coefficients[1]:.6f} * 面积 + {self.coefficients[2]:.6f}")

    def plot_calibration(self, areas: np.ndarray, reference_mcv: np.ndarray,
                        save_path: Optional[str] = None):
        """
        绘制校准曲线

        Args:
            areas: 校准用的面积数据
            reference_mcv: 校准用的参考MCV数据
            save_path: 图片保存路径（可选）
        """
        if not self.is_calibrated:
            raise RuntimeError("模型尚未校准")

        # 生成平滑的曲线用于绘图
        area_range = np.linspace(areas.min(), areas.max(), 100)
        predicted_curve = self.predict_mcv(area_range)
        predicted_points = self.predict_mcv(areas)

        plt.figure(figsize=(10, 6))

        # 绘制原始数据点
        plt.scatter(areas, reference_mcv, color='blue', alpha=0.6,
                   label='Calibration Data', s=50)

        # 绘制拟合曲线
        plt.plot(area_range, predicted_curve, color='red', linewidth=2,
                label=f'Fitted Curve (R² = {self.calibration_stats["r2_score"]:.4f})')

        # 绘制预测点
        plt.scatter(areas, predicted_points, color='red', alpha=0.8,
                   marker='x', s=30, label='Predicted Values')

        plt.xlabel('Mean RBC Area (μm²)')
        plt.ylabel('MCV (fL)')
        plt.title('MCV Calibration Curve')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 添加统计信息文本
        stats_text = f"RMSE: {self.calibration_stats['rmse']:.2f} fL\n"
        stats_text += f"MAE: {self.calibration_stats['mae']:.2f} fL\n"
        stats_text += f"Number of Samples: {self.calibration_stats['n_samples']}"
        plt.text(0.05, 0.95, stats_text, transform=plt.gca().transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat'))

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"校准曲线图已保存到: {save_path}")

        plt.show()

    def get_model_info(self) -> Dict:
        """
        获取模型信息

        Returns:
            模型信息字典
        """
        if not self.is_calibrated:
            return {"status": "未校准"}

        return {
            "status": "已校准",
            "coefficients": {
                "A": self.coefficients[0],
                "B": self.coefficients[1],
                "C": self.coefficients[2]
            },
            "calibration_stats": self.calibration_stats
        }

        

def calculate_calibration_coefficient(
    areas: List[float], 
    mcv_standards: List[float],
    model_type: Literal['linear', 'polynomial'] = 'linear',
    degree: int = 2
) -> Union[float, make_pipeline]:
    """
    计算校准系数
    
    参数:
        areas: 通过分割算法得到的红细胞平均面积列表
        mcv_standards: 对应的MCV金标准值列表
        model_type: 校准模型类型，'linear'或'polynomial'
        degree: 多项式回归的阶数（仅在model_type='polynomial'时使用）
    
    返回:
        linear: 返回单个校准系数
        polynomial: 返回多项式回归模型
    """
    # 将输入转换为numpy数组
    X = np.array(areas).reshape(-1, 1)
    y = np.array(mcv_standards)
    
    if model_type == 'linear':
        # 使用线性回归计算校准系数
        model = LinearRegression()
        model.fit(X, y)
        return model.coef_[0]
    else:
        # 使用多项式回归
        model = make_pipeline(PolynomialFeatures(degree), LinearRegression())
        model.fit(X, y)
        return model

def convert_area_to_mcv(
    area: float, 
    calibration_model: Union[float, make_pipeline],
    model_type: Literal['linear', 'polynomial'] = 'linear'
) -> float:
    """
    将红细胞平均面积转换为MCV值
    
    参数:
        area: 红细胞平均面积
        calibration_model: 校准模型或系数
        model_type: 校准模型类型
    
    返回:
        mcv: 计算得到的MCV值
    """
    if model_type == 'linear':
        return area * calibration_model
    else:
        return calibration_model.predict(np.array([[area]]))[0]

def calibrate_and_convert(
    areas: List[float], 
    mcv_standards: List[float],
    new_areas: List[float],
    model_type: Literal['linear', 'polynomial'] = 'linear',
    degree: int = 2
) -> Tuple[Union[float, make_pipeline], List[float]]:
    """
    计算校准系数并转换新的面积值
    
    参数:
        areas: 用于校准的红细胞平均面积列表
        mcv_standards: 对应的MCV金标准值列表
        new_areas: 需要转换的新红细胞平均面积列表
        model_type: 校准模型类型
        degree: 多项式回归的阶数
    
    返回:
        calibration_model: 校准模型或系数
        converted_mcv_values: 转换后的MCV值列表
    """
    # 计算校准系数
    calibration_model = calculate_calibration_coefficient(
        areas, mcv_standards, model_type, degree
    )
    
    # 转换新的面积值
    converted_mcv_values = [
        convert_area_to_mcv(area, calibration_model, model_type)
        for area in new_areas
    ]
    
    return calibration_model, converted_mcv_values

def plot_calibration(
    areas: List[float],
    mcv_standards: List[float],
    calibration_model: Union[float, make_pipeline],
    model_type: Literal['linear', 'polynomial'] = 'linear',
    title: str = "MCV Calibration Curve"
) -> None:
    """
    可视化校准效果
    
    参数:
        areas: 用于校准的红细胞平均面积列表
        mcv_standards: 对应的MCV金标准值列表
        calibration_model: 校准模型或系数
        model_type: 校准模型类型
        title: 图表标题
    """
    plt.figure(figsize=(10, 6))
    
    # Plot original data points
    plt.scatter(areas, mcv_standards, color='blue', label='Original Data')
    
    # 生成用于绘制曲线的点
    x_curve = np.linspace(min(areas), max(areas), 100).reshape(-1, 1)
    
    if model_type == 'linear':
        y_curve = x_curve * calibration_model
    else:
        y_curve = calibration_model.predict(x_curve)
    
    # Plot calibration curve
    plt.plot(x_curve, y_curve, color='red', label='Calibration Curve')
    
    plt.xlabel('Mean RBC Area')
    plt.ylabel('MCV Value')
    plt.title(title)
    plt.legend()
    plt.grid(True)
    plt.show()

# 使用示例
if __name__ == "__main__":
    # 示例数据
    calibration_areas = [100, 120, 140, 160, 180]  # 示例面积值
    mcv_standards = [80, 85, 90, 95, 100]  # 示例MCV标准值
    new_areas = [110, 130, 150]  # 需要转换的新面积值
    
    # 线性校准
    print("线性校准结果：")
    linear_model, linear_mcv = calibrate_and_convert(
        calibration_areas, mcv_standards, new_areas, 'linear'
    )
    print(f"线性校准系数: {linear_model:.4f}")
    print("转换后的MCV值:")
    for area, mcv in zip(new_areas, linear_mcv):
        print(f"面积 {area:.2f} -> MCV {mcv:.2f}")
    
    # 多项式校准
    print("\n多项式校准结果：")
    poly_model, poly_mcv = calibrate_and_convert(
        calibration_areas, mcv_standards, new_areas, 'polynomial', degree=2
    )
    print("转换后的MCV值:")
    for area, mcv in zip(new_areas, poly_mcv):
        print(f"面积 {area:.2f} -> MCV {mcv:.2f}")
    
    # 可视化校准效果
    plot_calibration(calibration_areas, mcv_standards, linear_model, 'linear', "Linear Calibration Curve")
    plot_calibration(calibration_areas, mcv_standards, poly_model, 'polynomial', "Polynomial Calibration Curve") 