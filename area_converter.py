"""
面积单位转换工具
用于在像素面积和实际面积(μm²)之间进行转换
"""

import pandas as pd
import numpy as np
from typing import Union, List, Optional
import os

class AreaConverter:
    """
    面积转换器类
    
    用于在像素面积和实际面积(μm²)之间进行转换
    转换公式: 实际面积(μm²) = 像素面积 * (像素大小(μm))²
    """
    
    def __init__(self, pixel_size_um: float):
        """
        初始化面积转换器
        
        Args:
            pixel_size_um: 像素大小(微米)
        """
        self.pixel_size_um = pixel_size_um
        self.pixel_to_um2_factor = pixel_size_um ** 2
        self.um2_to_pixel_factor = 1 / self.pixel_to_um2_factor
    
    def pixel_to_um2(self, pixel_area: Union[float, List[float], np.ndarray]) -> Union[float, np.ndarray]:
        """
        将像素面积转换为实际面积(μm²)
        
        Args:
            pixel_area: 像素面积或像素面积数组
            
        Returns:
            实际面积(μm²)或实际面积数组
        """
        return np.array(pixel_area) * self.pixel_to_um2_factor
    
    def um2_to_pixel(self, um2_area: Union[float, List[float], np.ndarray]) -> Union[float, np.ndarray]:
        """
        将实际面积(μm²)转换为像素面积
        
        Args:
            um2_area: 实际面积(μm²)或实际面积数组
            
        Returns:
            像素面积或像素面积数组
        """
        return np.array(um2_area) * self.um2_to_pixel_factor
    
    def convert_csv(self, 
                   input_file: str, 
                   output_file: str, 
                   column_name: str,
                   to_um2: bool = True,
                   new_column_name: Optional[str] = None) -> None:
        """
        转换CSV文件中的面积数据
        
        Args:
            input_file: 输入CSV文件路径
            output_file: 输出CSV文件路径
            column_name: 需要转换的列名
            to_um2: True表示像素转μm²，False表示μm²转像素
            new_column_name: 新列名，如果为None则使用原列名
        """
        # 读取CSV文件
        df = pd.read_csv(input_file)
        
        if column_name not in df.columns:
            raise ValueError(f"列名 '{column_name}' 在CSV文件中不存在")
        
        # 确定新列名
        if new_column_name is None:
            new_column_name = f"{column_name}_um2" if to_um2 else f"{column_name}_pixel"
        
        # 执行转换
        if to_um2:
            df[new_column_name] = self.pixel_to_um2(df[column_name])
        else:
            df[new_column_name] = self.um2_to_pixel(df[column_name])
        
        # 保存结果
        df.to_csv(output_file, index=False)
        print(f"转换完成！结果已保存到: {output_file}")

def main():
    """
    命令行使用示例
    """
    import argparse
    
    parser = argparse.ArgumentParser(description='面积单位转换工具')
    parser.add_argument('--pixel-size', type=float, required=True,
                      help='像素大小(微米)')
    parser.add_argument('--input', type=str, required=True,
                      help='输入CSV文件路径')
    parser.add_argument('--output', type=str, required=True,
                      help='输出CSV文件路径')
    parser.add_argument('--column', type=str, required=True,
                      help='需要转换的列名')
    parser.add_argument('--to-um2', action='store_true',
                      help='转换为μm² (默认)')
    parser.add_argument('--to-pixel', action='store_true',
                      help='转换为像素')
    parser.add_argument('--new-column', type=str,
                      help='新列名 (可选)')
    
    args = parser.parse_args()
    
    if args.to_pixel and args.to_um2:
        raise ValueError("不能同时指定 --to-um2 和 --to-pixel")
    
    converter = AreaConverter(args.pixel_size_um)
    converter.convert_csv(
        args.input,
        args.output,
        args.column,
        to_um2=not args.to_pixel,
        new_column_name=args.new_column
    )

if __name__ == "__main__":
    main() 